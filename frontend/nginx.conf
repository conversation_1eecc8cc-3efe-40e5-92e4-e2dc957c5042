events {
    worker_connections 1024;
}

# Set PID file location in /tmp which is writable
pid /tmp/nginx.pid;

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Disable access logs to avoid permission issues
    access_log off;
    error_log /dev/stderr;
    
    # Disable proxy buffering to avoid cache directory issues
    proxy_buffering off;
    client_body_temp_path /tmp/client_temp;
    proxy_temp_path /tmp/proxy_temp;
    fastcgi_temp_path /tmp/fastcgi_temp;
    uwsgi_temp_path /tmp/uwsgi_temp;
    scgi_temp_path /tmp/scgi_temp;
    
    server {
        listen 8080;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Proxy API requests to backend service
        location /api/ {
            proxy_pass http://placement-backend-service:8080/api/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # Serve static files - React SPA routing
        location / {
            try_files $uri $uri/ /index.html;
        }

        # Basic health check
        location /health {
            return 200 "OK";
            add_header Content-Type text/plain;
        }
    }
}
