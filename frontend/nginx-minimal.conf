events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Serve static files - React SPA routing
        location / {
            try_files $uri $uri/ /index.html;
        }

        # Basic health check
        location /health {
            return 200 "OK";
            add_header Content-Type text/plain;
        }
    }
}
