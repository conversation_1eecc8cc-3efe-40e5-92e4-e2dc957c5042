{"name": "placement-portal", "version": "1.0.0", "description": "MERN stack application for student placement management", "main": "index.js", "scripts": {"frontend": "cd frontend && npm run dev", "backend": "cd backend && npm run dev", "dev": "concurrently \"npm run backend\" \"npm run frontend\"", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd backend && npm run build && cd ../frontend && npm run build", "start": "cd backend && npm start"}, "keywords": ["mern", "react", "express", "mongodb", "placement"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^8.2.0"}}