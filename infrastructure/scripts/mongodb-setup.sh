#!/bin/bash

# MongoDB Setup Script for Ubuntu
set -e

# Variables
DB_USERNAME="${db_username}"
DB_PASSWORD="${db_password}"

# Log all output
exec > >(tee /var/log/mongodb-setup.log) 2>&1

echo "Starting MongoDB setup..."

# Update system
apt-get update -y
apt-get upgrade -y

# Install required packages
apt-get install -y curl wget gnupg lsb-release

# Import MongoDB public GPG key
curl -fsSL https://pgp.mongodb.com/server-7.0.asc | gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu $(lsb_release -cs)/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list

# Update package list
apt-get update -y

# Install MongoDB
apt-get install -y mongodb-org

# Start and enable MongoDB
systemctl start mongod
systemctl enable mongod

# Wait for MongoDB to start
sleep 10

# Configure MongoDB for external connections
sed -i 's/bindIp: 127.0.0.1/bindIp: 0.0.0.0/' /etc/mongod.conf

# Enable authentication
cat >> /etc/mongod.conf << EOF

security:
  authorization: enabled
EOF

# Restart MongoDB to apply configuration
systemctl restart mongod
sleep 10

# Create admin user and database
mongosh --eval "
use admin;
db.createUser({
  user: 'admin',
  pwd: '$DB_PASSWORD',
  roles: [{ role: 'userAdminAnyDatabase', db: 'admin' }]
});

use placement_db;
db.createUser({
  user: '$DB_USERNAME',
  pwd: '$DB_PASSWORD',
  roles: [{ role: 'readWrite', db: 'placement_db' }]
});

// Create some initial collections
db.users.insertOne({
  email: '<EMAIL>',
  password: '\$2b\$10\$rOvHq8K1mMZ8N9X7Y6Z5Z.K1mMZ8N9X7Y6Z5Z.K1mMZ8N9X7Y6Z5Z.',
  role: 'admin',
  name: 'Admin User',
  createdAt: new Date()
});

db.companies.insertOne({
  name: 'Sample Company',
  description: 'A sample company for testing',
  website: 'https://example.com',
  createdAt: new Date()
});

print('MongoDB setup completed successfully!');
"

# Create MongoDB status check script
cat > /usr/local/bin/mongodb-health-check.sh << 'EOF'
#!/bin/bash
if mongosh --quiet --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
    echo "MongoDB is healthy"
    exit 0
else
    echo "MongoDB is not responding"
    exit 1
fi
EOF

chmod +x /usr/local/bin/mongodb-health-check.sh

# Set up log rotation for MongoDB
cat > /etc/logrotate.d/mongodb << 'EOF'
/var/log/mongodb/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 mongodb mongodb
    postrotate
        /bin/kill -SIGUSR1 $(cat /var/lib/mongodb/mongod.lock 2>/dev/null) 2>/dev/null || true
    endscript
}
EOF

# Configure firewall (if ufw is enabled)
if ufw status | grep -q "Status: active"; then
    ufw allow 27017/tcp
fi

echo "MongoDB setup completed successfully!"
echo "Database: placement_db"
echo "Username: $DB_USERNAME"
echo "Connection string: mongodb://$DB_USERNAME:$DB_PASSWORD@$(hostname -I | awk '{print $1}'):27017/placement_db"

# Final health check
/usr/local/bin/mongodb-health-check.sh
