events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    sendfile on;
    keepalive_timeout 65;
    gzip on;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Serve static files
        location / {
            try_files $uri $uri/ /index.html;
        }

        # Proxy API requests to backend (only if backend is available)
        location /api/ {
            proxy_pass http://placement-backend-service.default.svc.cluster.local:5000/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_connect_timeout 5s;
            proxy_send_timeout 5s;
            proxy_read_timeout 5s;
            
            # If backend is not available, return error
            proxy_intercept_errors on;
            error_page 502 503 504 = @backend_unavailable;
        }

        # Fallback when backend is unavailable
        location @backend_unavailable {
            add_header Content-Type application/json;
            return 503 '{"error": "Backend service temporarily unavailable"}';
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "Frontend healthy\n";
            add_header Content-Type text/plain;
        }

        # Static assets with caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        error_page 404 /index.html;
    }
}
