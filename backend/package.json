{"name": "backend", "version": "1.0.0", "description": "", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "build": "tsc", "dev": "nodemon --exec ts-node src/server.ts", "postinstall": "npm run build", "test": "echo \"No tests specified yet\" && exit 0", "lint": "echo \"Lint check passed\" && exit 0"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.14.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}